import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";

export const runtime = "edge";

export async function POST(req: Request) {
  try {
    // Check if user is authenticated
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    // Get the form data from the request
    const formData = await req.formData();

    // Check if formData contains a file
    const imageFile = formData.get("file");
    if (!imageFile || !(imageFile instanceof File)) {
      return respErr("No image file provided");
    }

    // Create a new FormData instance to send to backend
    const backendFormData = new FormData();
    backendFormData.append("source", imageFile);

    // Forward the request to the FastAPI backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    const response = await fetch(`${backendUrl}/api/codeocr`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        source: await imageFile.text(),
      }),
    });

    // Check if the backend request was successful
    if (!response.ok) {
      console.error(
        "Backend request failed:",
        response.status,
        response.statusText
      );
      const errorText = await response.text();
      console.error("Backend error response:", errorText);
      return respErr(
        `Backend error: ${response.status} ${response.statusText}`
      );
    }

    // Parse the response from the backend
    const result = await response.json();

    // Check if the backend processing was successful
    if (result.status === "completed") {
      // Transform backend response to match frontend expectations
      const transformedData = {
        language: result.language,
        extracted_code: result.code,
        status: result.status,
      };
      return respData(transformedData);
    } else {
      console.error("Backend processing failed:", result);
      return respErr(result.message || "Failed to extract code from image");
    }
  } catch (error) {
    console.error("Error in codeocr API:", error);
    return respErr("Failed to process image");
  }
}

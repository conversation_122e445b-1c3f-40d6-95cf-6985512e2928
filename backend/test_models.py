"""Tests for Pydantic models used in CodeOCR API."""

import pytest
from pathlib import Path
from pydantic import ValidationError

from main import ImageInput, CodeOCRResponse


class TestImageInputModel:
    """Test cases for ImageInput Pydantic model."""

    def test_image_input_with_url(self):
        """Test ImageInput with a valid URL."""
        url = "https://example.com/image.png"
        image_input = ImageInput(source=url)
        
        assert image_input.source == url
        assert isinstance(image_input.source, str)

    def test_image_input_with_base64(self):
        """Test ImageInput with base64 encoded data."""
        base64_data = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
        image_input = ImageInput(source=base64_data)
        
        assert image_input.source == base64_data
        assert isinstance(image_input.source, str)

    def test_image_input_with_file_path_string(self):
        """Test ImageInput with file path as string."""
        file_path = "/path/to/image.png"
        image_input = ImageInput(source=file_path)
        
        assert image_input.source == file_path
        assert isinstance(image_input.source, str)

    def test_image_input_with_path_object(self):
        """Test ImageInput with Path object."""
        file_path = Path("/path/to/image.png")
        image_input = ImageInput(source=file_path)
        
        assert image_input.source == file_path
        assert isinstance(image_input.source, Path)

    def test_image_input_with_relative_path(self):
        """Test ImageInput with relative file path."""
        relative_path = "./images/code_screenshot.png"
        image_input = ImageInput(source=relative_path)
        
        assert image_input.source == relative_path

    def test_image_input_missing_source(self):
        """Test ImageInput validation fails when source is missing."""
        with pytest.raises(ValidationError) as exc_info:
            ImageInput()
        
        error = exc_info.value
        assert "source" in str(error)
        assert "Field required" in str(error)

    def test_image_input_invalid_type(self):
        """Test ImageInput validation fails with invalid source type."""
        with pytest.raises(ValidationError) as exc_info:
            ImageInput(source=123)  # Invalid type
        
        error = exc_info.value
        assert "source" in str(error)

    def test_image_input_none_source(self):
        """Test ImageInput validation fails with None source."""
        with pytest.raises(ValidationError) as exc_info:
            ImageInput(source=None)
        
        error = exc_info.value
        assert "source" in str(error)

    def test_image_input_empty_string(self):
        """Test ImageInput with empty string source."""
        # Empty string should be valid but might cause issues in processing
        image_input = ImageInput(source="")
        assert image_input.source == ""

    def test_image_input_serialization(self):
        """Test ImageInput model serialization."""
        url = "https://example.com/image.png"
        image_input = ImageInput(source=url)
        
        # Test dict serialization
        data = image_input.model_dump()
        assert data == {"source": url}
        
        # Test JSON serialization
        json_data = image_input.model_dump_json()
        assert url in json_data


class TestCodeOCRResponseModel:
    """Test cases for CodeOCRResponse Pydantic model."""

    def test_codeocr_response_complete(self):
        """Test CodeOCRResponse with all fields."""
        response = CodeOCRResponse(
            language="python",
            code="print('Hello, World!')",
            status="completed"
        )
        
        assert response.language == "python"
        assert response.code == "print('Hello, World!')"
        assert response.status == "completed"

    def test_codeocr_response_default_status(self):
        """Test CodeOCRResponse with default status."""
        response = CodeOCRResponse(
            language="javascript",
            code="console.log('Hello, World!');"
        )
        
        assert response.language == "javascript"
        assert response.code == "console.log('Hello, World!');"
        assert response.status == "completed"  # Default value

    def test_codeocr_response_different_languages(self):
        """Test CodeOCRResponse with various programming languages."""
        test_cases = [
            ("python", "def hello(): print('Hello')"),
            ("javascript", "function hello() { console.log('Hello'); }"),
            ("java", "public class Hello { public static void main(String[] args) { System.out.println(\"Hello\"); } }"),
            ("cpp", "#include <iostream>\nint main() { std::cout << \"Hello\"; return 0; }"),
            ("rust", "fn main() { println!(\"Hello\"); }"),
            ("go", "package main\nimport \"fmt\"\nfunc main() { fmt.Println(\"Hello\") }"),
            ("typescript", "const hello = (): void => { console.log('Hello'); }"),
        ]
        
        for language, code in test_cases:
            response = CodeOCRResponse(language=language, code=code)
            assert response.language == language
            assert response.code == code
            assert response.status == "completed"

    def test_codeocr_response_missing_language(self):
        """Test CodeOCRResponse validation fails when language is missing."""
        with pytest.raises(ValidationError) as exc_info:
            CodeOCRResponse(code="print('Hello')")
        
        error = exc_info.value
        assert "language" in str(error)
        assert "Field required" in str(error)

    def test_codeocr_response_missing_code(self):
        """Test CodeOCRResponse validation fails when code is missing."""
        with pytest.raises(ValidationError) as exc_info:
            CodeOCRResponse(language="python")
        
        error = exc_info.value
        assert "code" in str(error)
        assert "Field required" in str(error)

    def test_codeocr_response_empty_values(self):
        """Test CodeOCRResponse with empty string values."""
        response = CodeOCRResponse(
            language="",
            code="",
            status=""
        )
        
        assert response.language == ""
        assert response.code == ""
        assert response.status == ""

    def test_codeocr_response_multiline_code(self):
        """Test CodeOCRResponse with multiline code."""
        multiline_code = """def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))"""
        
        response = CodeOCRResponse(
            language="python",
            code=multiline_code
        )
        
        assert response.language == "python"
        assert response.code == multiline_code
        assert "\n" in response.code

    def test_codeocr_response_special_characters(self):
        """Test CodeOCRResponse with special characters in code."""
        code_with_special_chars = "print('Hello, 世界! 🌍 Special chars: @#$%^&*()')"
        
        response = CodeOCRResponse(
            language="python",
            code=code_with_special_chars
        )
        
        assert response.code == code_with_special_chars

    def test_codeocr_response_serialization(self):
        """Test CodeOCRResponse model serialization."""
        response = CodeOCRResponse(
            language="python",
            code="print('Hello, World!')",
            status="completed"
        )
        
        # Test dict serialization
        data = response.model_dump()
        expected = {
            "language": "python",
            "code": "print('Hello, World!')",
            "status": "completed"
        }
        assert data == expected
        
        # Test JSON serialization
        json_data = response.model_dump_json()
        assert "python" in json_data
        assert "print('Hello, World!')" in json_data
        assert "completed" in json_data

    def test_codeocr_response_custom_status(self):
        """Test CodeOCRResponse with custom status values."""
        custom_statuses = ["processing", "error", "failed", "partial", "success"]
        
        for status in custom_statuses:
            response = CodeOCRResponse(
                language="python",
                code="print('test')",
                status=status
            )
            assert response.status == status

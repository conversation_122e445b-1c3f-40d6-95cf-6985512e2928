"""Integration tests for CodeOCR API."""

import pytest
import os
from unittest.mock import patch, AsyncMock
from fastapi import status

from main import app


@pytest.mark.integration
class TestCodeOCRIntegration:
    """Integration tests for the complete CodeOCR workflow."""

    def test_complete_workflow_base64_image(self, test_client):
        """Test complete workflow with base64 image."""
        # Sample base64 image data (1x1 transparent PNG)
        image_data = {
            "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
        }
        
        # Mock the instructor client response
        mock_response = type('MockResponse', (), {
            'language': 'python',
            'code': 'def hello_world():\n    print("Hello, World!")\n\nhello_world()',
            'status': 'completed'
        })()
        
        with patch('main.client.chat') as mock_chat:
            mock_chat.completions.create = AsyncMock(return_value=mock_response)
            
            # Make the request
            response = test_client.post("/api/codeocr", json=image_data)
            
            # Verify response
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            
            assert data["language"] == "python"
            assert "def hello_world" in data["code"]
            assert data["status"] == "completed"
            
            # Verify the instructor client was called with correct parameters
            mock_chat.completions.create.assert_called_once()
            call_args = mock_chat.completions.create.call_args
            
            # Check that the model name is from environment
            assert call_args.kwargs["model"] == os.getenv("MODEL_NAME")
            assert call_args.kwargs["response_model"].__name__ == "CodeOCRResponse"

    def test_complete_workflow_image_url(self, test_client):
        """Test complete workflow with image URL."""
        image_data = {
            "source": "https://example.com/code-screenshot.png"
        }
        
        mock_response = type('MockResponse', (), {
            'language': 'javascript',
            'code': 'function greet(name) {\n    console.log(`Hello, ${name}!`);\n}\n\ngreet("World");',
            'status': 'completed'
        })()
        
        with patch('main.client.chat') as mock_chat:
            mock_chat.completions.create = AsyncMock(return_value=mock_response)
            
            response = test_client.post("/api/codeocr", json=image_data)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            
            assert data["language"] == "javascript"
            assert "function greet" in data["code"]
            assert data["status"] == "completed"

    def test_error_handling_invalid_image(self, test_client):
        """Test error handling with invalid image data."""
        invalid_image_data = {
            "source": "invalid-image-data"
        }
        
        # Mock instructor client to raise an exception
        with patch('main.client.chat') as mock_chat:
            mock_chat.completions.create = AsyncMock(side_effect=Exception("Invalid image format"))
            
            response = test_client.post("/api/codeocr", json=invalid_image_data)
            
            # Should return 500 Internal Server Error
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    def test_environment_variables_loaded(self, test_client):
        """Test that environment variables are properly loaded."""
        # Verify that the app starts successfully with environment variables
        response = test_client.get("/")
        assert response.status_code == status.HTTP_200_OK
        
        # Verify environment variables are set
        assert os.getenv("OPENAI_API_KEY") is not None
        assert os.getenv("MODEL_NAME") is not None

    @pytest.mark.asyncio
    async def test_async_workflow(self, async_client):
        """Test the complete workflow using async client."""
        image_data = {
            "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
        }
        
        mock_response = type('MockResponse', (), {
            'language': 'rust',
            'code': 'fn main() {\n    println!("Hello, World!");\n}',
            'status': 'completed'
        })()
        
        with patch('main.client.chat') as mock_chat:
            mock_chat.completions.create = AsyncMock(return_value=mock_response)
            
            response = await async_client.post("/api/codeocr", json=image_data)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            
            assert data["language"] == "rust"
            assert "fn main" in data["code"]
            assert data["status"] == "completed"


@pytest.mark.integration
class TestAPIPerformance:
    """Performance and load testing for the API."""

    def test_multiple_concurrent_requests(self, test_client):
        """Test handling multiple concurrent requests."""
        import concurrent.futures
        import threading
        
        image_data = {
            "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
        }
        
        def make_request():
            mock_response = type('MockResponse', (), {
                'language': 'python',
                'code': f'print("Request from thread {threading.current_thread().ident}")',
                'status': 'completed'
            })()
            
            with patch('main.client.chat') as mock_chat:
                mock_chat.completions.create = AsyncMock(return_value=mock_response)
                return test_client.post("/api/codeocr", json=image_data)
        
        # Make 5 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_request) for _ in range(5)]
            responses = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All requests should succeed
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "language" in data
            assert "code" in data
            assert "status" in data

    def test_large_image_data(self, test_client):
        """Test handling of large image data."""
        # Create a larger base64 string (simulating a larger image)
        large_base64 = "data:image/png;base64," + "A" * 10000  # 10KB of data
        
        image_data = {
            "source": large_base64
        }
        
        mock_response = type('MockResponse', (), {
            'language': 'python',
            'code': 'print("Large image processed successfully")',
            'status': 'completed'
        })()
        
        with patch('main.client.chat') as mock_chat:
            mock_chat.completions.create = AsyncMock(return_value=mock_response)
            
            response = test_client.post("/api/codeocr", json=image_data)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == "completed"


@pytest.mark.integration
class TestAPIDocumentationIntegration:
    """Integration tests for API documentation."""

    def test_openapi_schema_completeness(self, test_client):
        """Test that OpenAPI schema includes all endpoints and models."""
        response = test_client.get("/openapi.json")
        assert response.status_code == status.HTTP_200_OK
        
        schema = response.json()
        
        # Check basic structure
        assert "openapi" in schema
        assert "info" in schema
        assert "paths" in schema
        assert "components" in schema
        
        # Check endpoints are documented
        paths = schema["paths"]
        assert "/" in paths
        assert "/api/codeocr" in paths
        
        # Check models are documented
        components = schema["components"]
        assert "schemas" in components
        schemas = components["schemas"]
        assert "ImageInput" in schemas
        assert "CodeOCRResponse" in schemas
        
        # Check ImageInput schema
        image_input_schema = schemas["ImageInput"]
        assert "properties" in image_input_schema
        assert "source" in image_input_schema["properties"]
        
        # Check CodeOCRResponse schema
        codeocr_response_schema = schemas["CodeOCRResponse"]
        assert "properties" in codeocr_response_schema
        assert "language" in codeocr_response_schema["properties"]
        assert "code" in codeocr_response_schema["properties"]
        assert "status" in codeocr_response_schema["properties"]

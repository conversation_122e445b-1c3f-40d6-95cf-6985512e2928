"""Tests for CodeOCR FastAPI endpoints."""

import pytest
from fastapi import status
from unittest.mock import AsyncMock, patch

from main import CodeOCRResponse


class TestHealthEndpoint:
    """Test cases for the health check endpoint."""

    def test_root_endpoint_success(self, test_client):
        """Test that the root endpoint returns success status."""
        response = test_client.get("/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "ok"
        assert "CodeOCR API is running successfully!" in data["message"]

    @pytest.mark.asyncio
    async def test_root_endpoint_async(self, async_client):
        """Test the root endpoint with async client."""
        response = await async_client.get("/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "ok"
        assert "CodeOCR API is running successfully!" in data["message"]


class TestCodeOCREndpoint:
    """Test cases for the CodeOCR endpoint."""

    def test_codeocr_with_base64_image(self, test_client, mock_instructor_client, sample_image_data):
        """Test CodeOCR endpoint with base64 encoded image."""
        response = test_client.post("/api/codeocr", json=sample_image_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify response structure
        assert "language" in data
        assert "code" in data
        assert "status" in data
        
        # Verify response values
        assert data["language"] == "python"
        assert data["code"] == "print('Hello, World!')"
        assert data["status"] == "completed"
        
        # Verify instructor client was called
        mock_instructor_client.completions.create.assert_called_once()

    def test_codeocr_with_image_url(self, test_client, mock_instructor_client, sample_image_url):
        """Test CodeOCR endpoint with image URL."""
        response = test_client.post("/api/codeocr", json=sample_image_url)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["language"] == "python"
        assert data["code"] == "print('Hello, World!')"
        assert data["status"] == "completed"

    def test_codeocr_with_file_path(self, test_client, mock_instructor_client, sample_image_path):
        """Test CodeOCR endpoint with file path."""
        response = test_client.post("/api/codeocr", json=sample_image_path)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["language"] == "python"
        assert data["code"] == "print('Hello, World!')"
        assert data["status"] == "completed"

    def test_codeocr_missing_source(self, test_client):
        """Test CodeOCR endpoint with missing source field."""
        response = test_client.post("/api/codeocr", json={})
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data

    def test_codeocr_invalid_source_type(self, test_client):
        """Test CodeOCR endpoint with invalid source type."""
        invalid_data = {"source": 123}  # Should be string or Path
        response = test_client.post("/api/codeocr", json=invalid_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_codeocr_empty_source(self, test_client):
        """Test CodeOCR endpoint with empty source."""
        empty_data = {"source": ""}
        response = test_client.post("/api/codeocr", json=empty_data)
        
        # This should still pass validation but might fail in processing
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_422_UNPROCESSABLE_ENTITY, status.HTTP_500_INTERNAL_SERVER_ERROR]

    @pytest.mark.asyncio
    async def test_codeocr_async(self, async_client, mock_instructor_client, sample_image_data):
        """Test CodeOCR endpoint with async client."""
        response = await async_client.post("/api/codeocr", json=sample_image_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["language"] == "python"
        assert data["code"] == "print('Hello, World!')"
        assert data["status"] == "completed"


class TestCodeOCRErrorHandling:
    """Test error handling scenarios for CodeOCR endpoint."""

    def test_codeocr_instructor_client_error(self, test_client, sample_image_data, mocker):
        """Test CodeOCR endpoint when instructor client raises an exception."""
        # Mock instructor client to raise an exception
        mock_chat = AsyncMock()
        mock_chat.completions.create.side_effect = Exception("API Error")
        
        with patch('main.client.chat', mock_chat):
            response = test_client.post("/api/codeocr", json=sample_image_data)
            
            # Should return 500 Internal Server Error
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    def test_codeocr_with_different_languages(self, test_client, sample_image_data, mocker):
        """Test CodeOCR endpoint with different programming languages."""
        languages_and_codes = [
            ("javascript", "console.log('Hello, World!');"),
            ("java", "System.out.println(\"Hello, World!\");"),
            ("cpp", "#include <iostream>\nint main() { std::cout << \"Hello, World!\"; }"),
            ("rust", "fn main() { println!(\"Hello, World!\"); }"),
        ]
        
        for language, code in languages_and_codes:
            # Mock different responses
            mock_response = type('MockResponse', (), {
                'language': language,
                'code': code,
                'status': 'completed'
            })()
            
            mock_chat = AsyncMock()
            mock_chat.completions.create.return_value = mock_response
            
            with patch('main.client.chat', mock_chat):
                response = test_client.post("/api/codeocr", json=sample_image_data)
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["language"] == language
                assert data["code"] == code
                assert data["status"] == "completed"


class TestAPIDocumentation:
    """Test API documentation endpoints."""

    def test_openapi_docs_available(self, test_client):
        """Test that OpenAPI documentation is available."""
        response = test_client.get("/docs")
        assert response.status_code == status.HTTP_200_OK

    def test_openapi_json_available(self, test_client):
        """Test that OpenAPI JSON schema is available."""
        response = test_client.get("/openapi.json")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert data["info"]["title"] == "CodeOCR API"
